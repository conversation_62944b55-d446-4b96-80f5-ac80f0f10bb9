// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "./interfaces/IValidatorStorage.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";
import "./remigration/RemigrationLib.sol";

/**
 * @dev ValidatorStorageコントラクト
 *      Validatorデータのストレージ管理を行う
 *      CRUDのみを実装し、ビジネスロジックは含まない
 */
contract ValidatorStorage is Initializable, IValidatorStorage {
    using RemigrationLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;

    /** @dev ValidatorLogicコントラクトアドレス */
    address private _validatorLogicAddr;

    /** @dev 検証者ID配列 */
    bytes32[] private _validatorIds;

    /** @dev 検証者IDの存在確認フラグ(validatorId => boolean) */
    mapping(bytes32 => bool) private _validatorIdExistence;

    /** @dev 検証者IDに紐づくアカウントIdの存在確認フラグ(validatorId => accountId => boolean) */
    mapping(bytes32 => mapping(bytes32 => bool)) private _accountIdExistenceByValidatorId;

    /** @dev 発行者IDの紐付けフラグ(issuerId => boolean) */
    mapping(bytes32 => bool) private _issuerIdLinkedFlag;

    /** @dev 検証者データ */
    mapping(bytes32 => ValidatorData) private _validatorData;

    /** @dev setValidatorsAllのsignature検証用 */
    string private constant SET_VALIDATORS_ALL_SIGNATURE = "setValidatorsAll";

    ///////////////////////////////////
    // modifiers
    ///////////////////////////////////

    /**
     * @dev ValidatorLogicコントラクトからのみ呼び出し可能を保証するmodifier
     */
    modifier validatorLogicOnly() {
        require(msg.sender == _validatorLogicAddr, Error.INVALID_CALLER_ADDRESS);
        _;
    }

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(modifier)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    modifier adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0020_VALIDATOR_NOT_ADMIN_ROLE);
        _;
    }

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev 初期化関数
     * @param contractManager ContractManagerアドレス
     * @param validatorLogicAddr ValidatorLogicコントラクトアドレス
     */
    function initialize(IContractManager contractManager, address validatorLogicAddr)
        public
        initializer
    {
        require(address(contractManager) != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        require(validatorLogicAddr != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        _contractManager = contractManager;
        _validatorLogicAddr = validatorLogicAddr;
    }

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // ValidatorData CRUD操作
    ///////////////////////////////////

    /**
     * @dev 検証者データを取得する
     * @param validatorId 検証者ID
     * @return validatorData 検証者データ
     */
    function getValidatorData(bytes32 validatorId)
        external
        view
        validatorLogicOnly
        returns (ValidatorData memory validatorData)
    {
        return _validatorData[validatorId];
    }

    /**
     * @dev 検証者データを設定する
     * @param validatorId 検証者ID
     * @param validatorData 検証者データ
     */
    function setValidatorData(bytes32 validatorId, ValidatorData memory validatorData)
        external
        validatorLogicOnly
    {
        _validatorData[validatorId] = validatorData;
    }

    /**
     * @dev 検証者データの名前を更新する
     * @param validatorId 検証者ID
     * @param name 新しい名前
     */
    function updateValidatorName(bytes32 validatorId, bytes32 name) external validatorLogicOnly {
        _validatorData[validatorId].name = name;
    }

    /**
     * @dev 検証者データのロールを更新する
     * @param validatorId 検証者ID
     * @param role 新しいロール
     */
    function updateValidatorRole(bytes32 validatorId, bytes32 role) external validatorLogicOnly {
        _validatorData[validatorId].role = role;
    }

    /**
     * @dev 検証者データのバリデータアカウントIDを更新する
     * @param validatorId 検証者ID
     * @param validatorAccountId 新しいバリデータアカウントID
     */
    function updateValidatorAccountId(bytes32 validatorId, bytes32 validatorAccountId)
        external
        validatorLogicOnly
    {
        _validatorData[validatorId].validatorAccountId = validatorAccountId;
    }

    /**
     * @dev 検証者にアカウントIDを追加する
     * @param validatorId 検証者ID
     * @param accountId 追加するアカウントID
     */
    function addAccountIdToValidator(bytes32 validatorId, bytes32 accountId)
        external
        validatorLogicOnly
    {
        _validatorData[validatorId].accountIds.push(accountId);
    }

    ///////////////////////////////////
    // ValidatorIds配列操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDを配列に追加する
     * @param validatorId 追加する検証者ID
     */
    function addValidatorId(bytes32 validatorId) external validatorLogicOnly {
        _validatorIds.push(validatorId);
    }

    /**
     * @dev 検証者IDの配列を取得する
     * @return validatorIds 検証者IDの配列
     */
    function getValidatorIds() external view validatorLogicOnly returns (bytes32[] memory) {
        return _validatorIds;
    }

    /**
     * @dev 検証者IDを指定インデックスで取得する
     * @param index インデックス
     * @return validatorId 検証者ID
     */
    function getValidatorIdByIndex(uint256 index)
        external
        view
        validatorLogicOnly
        returns (bytes32)
    {
        require(index < _validatorIds.length, Error.UE0110_VALIDATOR_OUT_OF_INDEX);
        return _validatorIds[index];
    }

    /**
     * @dev 検証者IDの総数を取得する
     * @return count 検証者IDの総数
     */
    function getValidatorIdsCount() external view validatorLogicOnly returns (uint256) {
        return _validatorIds.length;
    }

    ///////////////////////////////////
    // ValidatorIdExistence マッピング操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDの存在フラグを取得する
     * @param validatorId 検証者ID
     * @return exists 存在フラグ
     */
    function getValidatorIdExistence(bytes32 validatorId)
        external
        view
        validatorLogicOnly
        returns (bool)
    {
        return _validatorIdExistence[validatorId];
    }

    /**
     * @dev 検証者IDの存在フラグを設定する
     * @param validatorId 検証者ID
     * @param exists 存在フラグ
     */
    function setValidatorIdExistence(bytes32 validatorId, bool exists) external validatorLogicOnly {
        _validatorIdExistence[validatorId] = exists;
    }

    ///////////////////////////////////
    // AccountIdExistenceByValidatorId マッピング操作
    ///////////////////////////////////

    /**
     * @dev 検証者IDに紐づくアカウントIDの存在フラグを取得する
     * @param validatorId 検証者ID
     * @param accountId アカウントID
     * @return exists 存在フラグ
     */
    function getAccountIdExistenceByValidatorId(bytes32 validatorId, bytes32 accountId)
        external
        view
        validatorLogicOnly
        returns (bool)
    {
        return _accountIdExistenceByValidatorId[validatorId][accountId];
    }

    /**
     * @dev 検証者IDに紐づくアカウントIDの存在フラグを設定する
     * @param validatorId 検証者ID
     * @param accountId アカウントID
     * @param exists 存在フラグ
     */
    function setAccountIdExistenceByValidatorId(
        bytes32 validatorId,
        bytes32 accountId,
        bool exists
    ) external validatorLogicOnly {
        _accountIdExistenceByValidatorId[validatorId][accountId] = exists;
    }

    ///////////////////////////////////
    // IssuerIdLinkedFlag マッピング操作
    ///////////////////////////////////

    /**
     * @dev 発行者IDの紐付けフラグを取得する
     * @param issuerId 発行者ID
     * @return linked 紐付けフラグ
     */
    function getIssuerIdLinkedFlag(bytes32 issuerId)
        external
        view
        validatorLogicOnly
        returns (bool)
    {
        return _issuerIdLinkedFlag[issuerId];
    }

    /**
     * @dev 発行者IDの紐付けフラグを設定する
     * @param issuerId 発行者ID
     * @param linked 紐付けフラグ
     */
    function setIssuerIdLinkedFlag(bytes32 issuerId, bool linked) external validatorLogicOnly {
        _issuerIdLinkedFlag[issuerId] = linked;
    }

    ///////////////////////////////////
    // バックアップ・リストア関連
    ///////////////////////////////////

    /**
     * @dev バックアップ用に全検証者データを設定する（Admin権限必要）
     * @param validator 全検証者データ
     * @param deadline 署名の期限
     * @param signature Admin署名
     */
    function setValidatorAll(
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    )
        external
        override
        adminOnly(
            keccak256(abi.encode(SET_VALIDATORS_ALL_SIGNATURE, deadline)),
            deadline,
            signature
        )
    {
        // ValidatorIdをまず配列に追加
        _validatorIds.push(validator.validatorId);

        // RemigrationLibを使用してデータを設定
        RemigrationLib.setValidatorAll(
            _validatorData[validator.validatorId],
            _accountIdExistenceByValidatorId[validator.validatorId],
            _validatorIdExistence,
            _issuerIdLinkedFlag,
            address(_contractManager),
            validator
        );
    }

    /**
     * @dev バックアップ用に検証者データを取得する
     * @param index インデックス
     * @return validator 全検証者データ
     */
    function getValidatorAll(uint256 index)
        external
        view
        override
        returns (ValidatorAll memory validator)
    {
        require(index < _validatorIds.length, Error.UE0110_VALIDATOR_OUT_OF_INDEX);
        bytes32 validatorId = _validatorIds[index];

        // RemigrationLibを使用してデータを取得
        return
            RemigrationLib.getValidatorAll(
                _validatorData[validatorId],
                _accountIdExistenceByValidatorId[validatorId],
                _validatorIdExistence,
                _issuerIdLinkedFlag,
                address(_contractManager),
                validatorId
            );
    }

    ///////////////////////////////////
    // 管理機能
    ///////////////////////////////////

    /**
     * @dev ContractManagerアドレスを更新する（将来の拡張用）
     * @param contractManagerAddr 新しいContractManagerアドレス
     */
    function setContractManagerAddress(address contractManagerAddr) external {
        // Admin権限チェック（署名なしでの直接的なアクセス制御）
        // require(
        //     _contractManager.accessCtrl().hasAdminRole(msg.sender),
        //     Error.GA0020_VALIDATOR_NOT_ADMIN_ROLE
        // );
        require(contractManagerAddr != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        _contractManager = IContractManager(contractManagerAddr);
    }

    /**
     * @dev ContractManagerアドレスを取得する
     * @return contractManagerAddr ContractManagerアドレス
     */
    function getContractManagerAddress() external view override returns (address) {
        return address(_contractManager);
    }

    /**
     * @dev ValidatorLogicアドレスを更新する（Admin権限必要）
     * @param validatorLogicAddr 新しいValidatorLogicアドレス
     */
    function setValidatorLogicAddress(address validatorLogicAddr) external {
        // Admin権限チェック
        // require(
        //     _contractManager.accessCtrl().hasAdminRole(msg.sender),
        //     Error.GA0020_VALIDATOR_NOT_ADMIN_ROLE
        // );
        require(validatorLogicAddr != address(0), Error.RV0009_VALIDATOR_INVALID_VAL);
        _validatorLogicAddr = validatorLogicAddr;
    }
}
