// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/cryptography/ECDSAUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/utils/math/SafeMathUpgradeable.sol";

import "./interfaces/IAccessCtrl.sol";
import "./crypto/Secp256k1.sol";
import "./crypto/Secp256k1Curve.sol";
import "./interfaces/IContractManager.sol";
import "./interfaces/Error.sol";

/**
 * @dev AccessCtrlコントラクト
 */
contract AccessCtrl is IAccessCtrl, Initializable, AccessControlUpgradeable, Secp256k1Curve {
    ///////////////////////////////////
    // public const variables
    ///////////////////////////////////

    // solhint-disable state-visibility
    /// @dev Providerロール計算用(calcRole()prefix用文字列(Provider権限))
    bytes32 public constant ROLE_PREFIX_PROV = keccak256("PROV_ROLE");
    /// @dev Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限))
    bytes32 public constant ROLE_PREFIX_ISSUER = keccak256("ISSUER_ROLE");
    /// @dev Validatorロール計算用(calcRole()のprefix用文字列(Validator権限))
    bytes32 public constant ROLE_PREFIX_VALIDATOR = keccak256("VALIDATOR_ROLE");
    /// @dev Validatorロール計算用(calcRole()のprefix用文字列(Validator権限))
    bytes32 public constant ROLE_PREFIX_ACCOUNT = keccak256("ACCOUNT_ROLE");
    // solhint-enable state-visibility

    ///////////////////////////////////
    // private const variables
    ///////////////////////////////////

    /// @dev Adminロール値
    bytes32 private constant _ROLE_ADMIN = keccak256("ADMIN_ROLE");

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /// @dev Providerコントラクト
    IContractManager private _contractManager;
    /// @dev EOA(id => address)
    mapping(bytes32 => address) private _idToAddress;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     * @param contractManager ContractManagerアドレス
     * @param eoaAdmin ADMIN権限を持たせるEOA
     */
    function initialize(IContractManager contractManager, address eoaAdmin) public initializer {
        _contractManager = contractManager;

        // このプロジェクトではmsg.senderを使わない方式のため、
        // AccessControlの_setRoleAdmin()やgrantRole()などは使用しない。
        // その代わり_setupRole()を使うので呼び出し前のチェックは必ず行うこと。
        _setupRole(_ROLE_ADMIN, eoaAdmin);

        // EOA削除権限を与える
        _setupRole(DEFAULT_ADMIN_ROLE, eoaAdmin);
    }

    /**
     * @dev コントラクトバージョン取得
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev 権限アドレス追加(Admin)。Adminの権限が必要。
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param eoaNew 権限を付与するEOA
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addAdminRole(
        address eoaNew,
        uint256 deadline,
        bytes memory signature
    ) external override {
        bytes32 hash = keccak256(abi.encode(eoaNew, deadline));
        (bool has, string memory errTmp) = _checkRole(_ROLE_ADMIN, hash, deadline, signature);
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.GA0003_ACTRL_NOT_ADMIN_ROLE);

        _setupRole(_ROLE_ADMIN, eoaNew);
    }

    /**
     * @dev 権限アドレス追加(Admin以外)。<br/>
     *      Admin以外の権限値はcalcRole()で計算した値を使用し、
     *      各Providerや各Issuerごとに権限値が用意される想定である。<br/>
     *      Adminの権限が必要。
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addRole(
        bytes32 role,
        address eoaNew,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // addRole()でADMINは許可しない
        require(role != _ROLE_ADMIN, Error.RV0001_ACTRL_BAD_ROLE);

        bytes32 hash = keccak256(abi.encode(role, eoaNew, deadline));
        (bool has, string memory errTmp) = _checkRole(_ROLE_ADMIN, hash, deadline, signature);
        require(bytes(errTmp).length == 0, errTmp);
        require(has, Error.GA0003_ACTRL_NOT_ADMIN_ROLE);

        _setupRole(role, eoaNew);
    }

    /**
     * @dev 権限アドレス追加(Admin以外)(Providerコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param providerId ProviderID
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByProv(
        bytes32 providerId,
        bytes32 role,
        address eoaNew
    ) external override {
        // addRole()でADMINは許可しない
        require(role != _ROLE_ADMIN, Error.RV0001_ACTRL_BAD_ROLE);
        require(
            msg.sender == address(_contractManager.provider()),
            Error.GA0005_NOT_PROVIDER_CONTRACT
        );

        _idToAddress[providerId] = eoaNew;
        _setupRole(role, eoaNew);
    }

    /**
     * @dev 権限アドレス追加(Admin以外)(Issuerコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param issuerId 発行者ID
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByIssuer(
        bytes32 issuerId,
        bytes32 role,
        address eoaNew
    ) external override {
        // addRole()でADMINは許可しない
        require(role != _ROLE_ADMIN, Error.RV0001_ACTRL_BAD_ROLE);
        require(msg.sender == address(_contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);

        _idToAddress[issuerId] = eoaNew;
        _setupRole(role, eoaNew);
    }

    /**
     * @dev 権限アドレス追加(Admin以外)(Validatorコントラクト専用)。<br/>
     *      addRole()参照
     * ```
     * emit event: RoleGranted()
     * ```
     *
     * @param role 権限
     * @param eoaNew 権限を付与するEOA
     */
    function addRoleByValidator(
        bytes32 validatorId,
        bytes32 role,
        address eoaNew
    ) external override {
        // addRole()でADMINは許可しない
        require(role != _ROLE_ADMIN, Error.RV0001_ACTRL_BAD_ROLE);
        require(
            msg.sender == address(_contractManager.validator()) ||
                msg.sender == address(_contractManager.validatorStorage()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        _idToAddress[validatorId] = eoaNew;
        _setupRole(role, eoaNew);
    }

    /**
     * @dev AccountのEoaを追加
     */
    function addAccountEoa(bytes32 accountId, address eoa) external override {
        require(
            msg.sender == address(_contractManager.account()),
            Error.GA0012_NOT_ACCOUNT_CONTRACT
        );

        _idToAddress[accountId] = eoa;
    }

    /**
     * @dev
     * Admin権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     * コントラクトをデプロイしたアドレスは削除できない。
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param eoaDel 権限を削除するEOA
     */
    function delAdminRole(address eoaDel) external override {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), Error.RV0001_ACTRL_BAD_ROLE);

        // admin role権限を持つ場合は削除できない
        require(!hasRole(DEFAULT_ADMIN_ROLE, eoaDel), Error.RV0001_ACTRL_BAD_ROLE);

        revokeRole(_ROLE_ADMIN, eoaDel);
    }

    /**
     * @dev
     * Provider権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param provID プロバイダID
     * @param eoaDel 権限を削除するEOA
     */
    function delProviderRole(bytes32 provID, address eoaDel) external override {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), Error.RV0001_ACTRL_BAD_ROLE);
        revokeRole(calcRole(ROLE_PREFIX_PROV, provID), eoaDel);
        delete _idToAddress[provID];
    }

    /**
     * @dev
     * Issuer権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param issuerID 発行者ID
     * @param eoaDel 権限を削除するEOA
     */
    function delIssuerRole(bytes32 issuerID, address eoaDel) external override {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), Error.RV0001_ACTRL_BAD_ROLE);
        revokeRole(calcRole(ROLE_PREFIX_ISSUER, issuerID), eoaDel);
        delete _idToAddress[issuerID];
    }

    /**
     * @dev
     * Validator権限アドレス削除。<br/>
     * msg.senderがコントラクトをデプロイしたアドレスであること。<br/>
     *
     * ```
     * emit event: RoleRevoked()
     * ```
     *
     * @param validID 検証者ID
     * @param eoaDel 権限を削除するEOA
     */
    function delValidRole(bytes32 validID, address eoaDel) external override {
        require(hasRole(DEFAULT_ADMIN_ROLE, msg.sender), Error.RV0001_ACTRL_BAD_ROLE);
        revokeRole(calcRole(ROLE_PREFIX_VALIDATOR, validID), eoaDel);
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev 権限値計算。権限(role)として大きくAdmin、Admin以外、無し、の3つのどれかに属する。
     *      Admin以外ではProvider, Issuerがあるが、権限の有効範囲は各Providerや各Issuerに
     *      属するものだけである。
     *      ここでは各Providerや各Issuer用の権限値を作成する。
     *      prefixを用意したのは、idが連番になると値が重複してしまうためである。
     * @param prefix ROLE_PREFIX_xxx()を想定(チェックはしない)
     * @param id 権限を割り振るID
     * @return 権限値
     */
    function calcRole(bytes32 prefix, bytes32 id) public pure override returns (bytes32) {
        return keccak256(abi.encode(prefix, id));
    }

    /**
     * @dev 権限チェック(Admin)。署名からEOAを復元し、Admin権限を持つかチェックする
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:Admin権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkAdminRole(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        return _checkRole(_ROLE_ADMIN, hash, deadline, signature);
    }

    /**
     * @dev 権限チェック(Admin以外)。署名からEOAを復元し、対象の権限を持つかチェックする。
     * @param role チェック対象の権限値
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:該当の権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function checkRole(
        bytes32 role,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        return _checkRole(role, hash, deadline, signature);
    }

    /**
     * @dev 権限チェック(内部用)。
     * @param role チェック対象の権限値
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     * @return has true:該当の権限あり
     * @return err 空文字列では無い場合、エラー有り(その他の戻り値は無効)
     */
    function _checkRole(
        bytes32 role,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view returns (bool has, string memory err) {
        if (role == 0) {
            // 主に未登録の場合
            return (false, Error.GA0002_ACTRL_NOT_ROLE);
        }

        // solhint-disable not-rely-on-time
        if (deadline < block.timestamp) {
            return (false, Error.GA0001_ACTRL_SIG_TIMEOUT);
        }
        // solhint-enable not-rely-on-time

        address addr = _recoverAddr(hash, signature);
        if (addr == address(0)) {
            return (false, Error.GS0001_ACTRL_BAD_SIG);
        }
        has = hasRole(role, addr);
    }

    /**
     * @dev 署名からのアドレス復元(内部用)。
     * @param hash signatureの計算元ハッシュ値
     * @param signature 権限チェック対象の署名
     * @return 復元したアドレス
     */
    function _recoverAddr(bytes32 hash, bytes memory signature) internal pure returns (address) {
        if (signature.length != 65) {
            return address(0);
        }
        bytes32 msgHash = ECDSAUpgradeable.toEthSignedMessageHash(hash);
        return ECDSAUpgradeable.recover(msgHash, signature);
    }

    function _pubRecover(uint256 x, uint256 y) internal pure returns (address addr) {
        /* solhint-disable no-inline-assembly */
        bytes memory b = new bytes(64);
        assembly {
            mstore(add(b, 32), x)
        }
        assembly {
            mstore(add(b, 64), y)
        }

        bytes32 h = keccak256(b);
        addr = address(uint160(uint256(h)));
        return addr;
        /* solhint-disable no-inline-assembly */
    }

    // Validatorの証明書検証
    function _checkValidatorSig_deep(
        bytes32 validatorId,
        uint256 pt,
        bytes memory pkc,
        bytes memory sigcpt
    ) internal view returns (bool has, string memory err) {
        // solhint-disable not-rely-on-time
        if (pt < block.timestamp) {
            return (false, Error.GS0003_ACCOUNT_INVALID_SIG);
        }
        bytes32 hash = keccak256(abi.encode(pkc, pt));
        address eoav = _recoverAddr(hash, sigcpt);

        if (eoav != _idToAddress[validatorId]) {
            return (false, Error.GS0002_ACCOUNT_BAD_SIG);
        }
        return (true, "");
    }

    // アカウントの署名検証
    function checkSigAccount(
        bytes32 accountId,
        bytes32 hashAccount,
        bytes memory accountSignature,
        bytes memory info
    ) external view override returns (bool has, string memory err) {
        bytes memory pko;
        bytes memory pkc;
        uint256 pt;
        bytes memory sigcpt;

        // EncodeされたsignatureをDecodeする
        (pko, pkc, pt, sigcpt) = abi.decode(info, (bytes, bytes, uint256, bytes));

        // ①Validatorの証明書検証 stack too deepを回避する為に別関数で検証
        // TODO: 現状のアーキテクチャではBizZoneから銀行署名の検証ができないため、一時的にコメントアウトする。今後Core側の修正が完了した段階で修正を行う。
        // (has, err) = _checkValidatorSig_deep(validatorId, pt, pkc, sigcpt);
        // if (bytes(err).length != 0) {
        //     return (false, err);
        // } else if (has == false) {
        //     return (false, Error.ACCOUNT_BAD_SIG); // TODO: 現状の_checkValidatorSig_deepの実装として、bytes(err).length == 0かつhas == falseであるreturnを行うパターンが存在しない
        // }

        // ②OneTime公開鍵検証 stack too deepを回避する為に別関数で検証
        (has, err) = _checkAccountOneTime_deep(accountId, pko, pkc);
        if (bytes(err).length != 0) {
            return (false, err);
        } else if (has == false) {
            return (false, Error.GS0002_ACCOUNT_BAD_SIG); // TODO: 現状の_checkAccountOneTime_deepの実装として、bytes(err).length == 0かつhas == falseであるreturnを行うパターンが存在しない
        }

        // ③Accountの署名検証 stack too deepを回避する為に別関数で検証
        (has, err) = _checkAccountSig_deep(pko, hashAccount, accountSignature);
        if (bytes(err).length != 0) {
            return (false, err);
        } else if (has == false) {
            return (false, Error.GS0002_ACCOUNT_BAD_SIG); // TODO: 現状の_checkAccountOneTime_deepの実装として、bytes(err).length == 0かつhas == falseであるreturnを行うパターンが存在しない
        }

        return (true, "");
    }

    /// @dev ecAddRecover recover an address
    /// @param pox Px of public key O
    /// @param pkoSign compress flag of public key O
    /// @param pcx Px of public key C
    /// @param pkcSign Px compress flag of public key C
    /// @param addr address of PKa.
    function eccAddRecover(
        uint256 pox,
        uint256 pkoSign,
        uint256 pcx,
        uint256 pkcSign
    ) external view override returns (address addr) {
        /* solhint-disable no-inline-assembly */
        uint256[2] memory po;
        uint256[2] memory pc;
        if (pkoSign == 0x02) {
            po = this.decompress(0, pox);
            if (pkcSign == 0x03) {
                pc = this.decompress(0, pcx);
            } else {
                pc = this.decompress(1, pcx);
            }
        } else {
            po = this.decompress(1, pox);
            if (pkcSign == 0x03) {
                pc = this.decompress(0, pcx);
            } else {
                pc = this.decompress(1, pcx);
            }
        }

        (uint256 x, uint256 y) = addPoint(po, pc);

        bytes memory b = new bytes(64);
        assembly {
            mstore(add(b, 32), x)
        }
        assembly {
            mstore(add(b, 64), y)
        }

        bytes32 h = keccak256(b);
        addr = address(uint160(uint256(h)));
        return addr;
        /* solhint-disable no-inline-assembly */
    }

    // Field size
    uint256 constant pp = 0xFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFEFFFFFC2F;

    function addPoint(uint256[2] memory po, uint256[2] memory pc)
        internal
        pure
        returns (uint256, uint256)
    {
        uint256 p = pp;

        uint256[3] memory go = Secp256k1._mul(1, po);
        uint256[3] memory gc = Secp256k1._mul(1, pc);

        uint256[3] memory P = Secp256k1._add(go, gc);

        uint256 zInv = ECCMath.invmod(P[2], p);
        uint256 zInv2 = mulmod(zInv, zInv, p);

        uint256 x = mulmod(P[0], zInv2, p);
        uint256 y = mulmod(P[1], mulmod(zInv, zInv2, p), p);

        return (x, y);
    }

    // Uint256に変換を行う
    function toUint256(bytes memory _bytes, uint256 _start) internal pure returns (uint256) {
        /* solhint-disable no-inline-assembly */
        require(_bytes.length >= _start + 32, "toUint256_outOfBounds");
        uint256 tempUint;

        assembly {
            tempUint := mload(add(add(_bytes, 0x20), _start))
        }
        return tempUint;
        /* solhint-disable no-inline-assembly */
    }

    // AccountのOneTime検証用関数
    function _checkAccountOneTime_deep(
        bytes32 accountId,
        bytes memory pko,
        bytes memory pkc
    ) internal view returns (bool has, string memory err) {
        // pkoからフラグと座標を分離する。編集する前の状態pkoxの長さが全体の長さ
        uint8 pkoSign = uint8(bytes1(pko[0]));
        // pkcからフラグと座標を分離する。編集する前の状態pkcxの長さが全体の長さ
        uint8 pkcSign = uint8(bytes1(pkc[0]));
        // pkoからx座標を抽出する
        uint256 pkox = toUint256(pko, 1);
        // pkcからx座標を抽出する
        uint256 pkcx = toUint256(pkc, 1);

        address eoaa = this.eccAddRecover(pkox, pkoSign, pkcx, pkcSign);
        if (eoaa != _idToAddress[accountId]) {
            return (false, Error.GS0002_ACCOUNT_BAD_SIG);
        }
        return (true, "");
    }

    // AccountのSignatureを検証する
    function _checkAccountSig_deep(
        bytes memory pko,
        bytes32 hashAccount,
        bytes memory accountSignature
    ) internal view returns (bool has, string memory err) {
        // pkoxからフラグと座標を分離する。編集する前の状態pkoxの長さが全体の長さ
        uint8 pkoSign = uint8(bytes1(pko[0]));
        // pkoxからx座標を抽出する
        uint256 pkox = toUint256(pko, 1);
        address eoao = _recoverAddr(hashAccount, accountSignature);
        uint256[2] memory pubo;
        if (pkoSign == 0x02) {
            pubo = this.decompress(0, pkox);
        } else {
            pubo = this.decompress(1, pkox);
        }

        address eoaoRecover = _pubRecover(pubo[0], pubo[1]);
        if (eoao != eoaoRecover) {
            return (false, Error.GS0002_ACCOUNT_BAD_SIG);
        }
        return (true, "");
    }

    /**
     * @dev ProviderIDからEOAを逆引きする。Providerコントラクトのみ実行を許す。
     * @param providerId ProviderId
     * @return providerEoa
     */
    function getProviderEoa(bytes32 providerId) public view override returns (address) {
        require(
            msg.sender == address(_contractManager.provider()),
            Error.GA0005_NOT_PROVIDER_CONTRACT
        );
        return _idToAddress[providerId];
    }

    /**
     * @dev IssuerIDからEOAを逆引きする。Issuerコントラクトのみ実行を許す。
     * @param issuerId IssuerId
     * @return issuerEoa
     */
    function getIssuerEoa(bytes32 issuerId) public view override returns (address) {
        // Issuerコントラクトからの呼び出しである事が条件
        require(msg.sender == address(_contractManager.issuer()), Error.GA0008_NOT_ISSUER_CONTRACT);
        return _idToAddress[issuerId];
    }

    /**
     * @dev ValidatorIDからEOAを逆引きする。Validatorコントラクトのみ実行を許す。
     * @param validatorId ValidatorId
     * @return validatorEoa
     */
    function getValidatorEoa(bytes32 validatorId) public view override returns (address) {
        // Validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.validatorStorage()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        return _idToAddress[validatorId];
    }

    /**
     * @dev AccountIDからEOAを逆引きする。Accountコントラクトのみ実行を許す。
     * @param accountId AccountId
     * @return accountEoa
     */
    function getAccountEoa(bytes32 accountId) public view override returns (address) {
        // Validatorコントラクトからの呼び出しである事が条件
        require(
            msg.sender == address(_contractManager.account()),
            Error.GA0012_NOT_ACCOUNT_CONTRACT
        );
        return _idToAddress[accountId];
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;

    // テスト用の公開ラッパー関数
    function testCheckValidatorSig_deep(
        bytes32 validatorId,
        uint256 pt,
        bytes memory pkc,
        bytes memory sigcpt
    ) external view returns (bool has, string memory err) {
        return _checkValidatorSig_deep(validatorId, pt, pkc, sigcpt);
    }
}
