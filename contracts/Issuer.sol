// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";

// ライブラリのimport
import "./libraries/IssuerLib.sol";
import "./remigration/RemigrationLib.sol";
import {IssuerData} from "./interfaces/Struct.sol";
import "./interfaces/Error.sol";

/**
 * @dev Issuerコントラクト
 */
contract Issuer is Initializable, IIssuer {
    ///////////////////////////////////
    // libraries
    ///////////////////////////////////

    using IssuerLib for *;

    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    /** @dev ContractManagerアドレス */
    IContractManager private _contractManager;
    /** @dev 検証者ID */
    bytes32[] private _issuerIds;
    /** @dev 発行者IDの存在確認フラグ(issuerId => boolean) */
    mapping(bytes32 => bool) private _issuerIdExistence;
    /** @dev 発行者IDに紐づくアカウントIdの存在確認フラグ(issuerId => accountId => boolean) */
    mapping(bytes32 => mapping(bytes32 => bool)) private _accountIdExistenceByIssuerId;
    /** @dev 発行者データ */
    mapping(bytes32 => IssuerData) private _issuerData;
    /** ディーカレット向けのbankCode */
    uint256 private constant _DECCURET_BANK_CODE = 9999;
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_ISSUERS_LIMIT = 1000;
    /** @dev getIssuersAllのsignature検証用 **/
    string private constant _GET_ISSUERS_ALL_SIGNATURE = "getIssuersAll";
    /* @dev setIssuersAllのsignature検証用 */
    string private constant _SET_ISSUERS_ALL_SIGNATURE = "setIssuersAll";
    /// @dev Issuerロール計算用(calcRole()のprefix用文字列(Issuer権限))
    bytes32 public constant ROLE_PREFIX_ISSUER = keccak256("ISSUER_ROLE");

    bytes32 private constant _EMPTY_BYTES32 = 0x00;
    uint16 private constant _EMPTY_UINT = 0;

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0009_ISSUER_NOT_ADMIN_ROLE);
    }

    /**
     * @dev Issuer権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param issuerId issuerId
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _issuerOnly(
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
            _issuerData.getIssuerRole(issuerId),
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.GA0007_ISSUER_NOT_ROLE);
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Issuerの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param bankCode 金融機関コード
     * @param name issuer名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addIssuer(
        bytes32 issuerId,
        uint16 bankCode,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, bankCode, name, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // DeccuretIssuer以外が最初のIssuerとして登録されることを許容しない
        if (bankCode != _DECCURET_BANK_CODE) {
            require(_issuerIds.length > 0, Error.GE2004_ISSUER_NOT_DECURRET);
        }

        // bankCodeが重複していないかチェック
        for (uint256 i = 0; i < _issuerIds.length; i++) {
            require(
                _issuerData[_issuerIds[i]].bankCode != bankCode,
                Error.GE1009_ISSUER_EXIST_BANK_CODE
            );
        }

        _issuerIds.addIssuerId(issuerId, _issuerIdExistence[issuerId]);
        _issuerIdExistence[issuerId] = true;
        bytes32 role = _contractManager.accessCtrl().calcRole(ROLE_PREFIX_ISSUER, issuerId);

        _issuerData.addIssuer(issuerId, role, name, bankCode);

        emit AddIssuer(issuerId, bankCode, name, traceId);
    }

    /**
     * @dev issuerにaccountを紐付ける。
     *
     * ```
     * emit event: AddAccountByIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param traceId トレースID
     */
    function addAccountId(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        require(
            msg.sender == address(_contractManager.validator()),
            Error.GA0019_NOT_VALIDATOR_CONTRACT
        );
        // issuerID有効性チェック
        {
            (bool success, string memory errTmp) = _hasIssuer(issuerId);
            require(success, errTmp);
        }
        // Issuerにアカウントが紐付いているかチェックを行う
        require(!_accountIdExistenceByIssuerId[issuerId][accountId], Error.GE1010_ACCOUNT_ID_EXIST);
        _issuerData.addAccountId(issuerId, accountId);
        _accountIdExistenceByIssuerId[issuerId][accountId] = true;
        emit AddAccountByIssuer(issuerId, accountId, traceId);
    }

    /**
     * @dev issuer権限の追加。
     *
     * ```
     * emit event: AddIssuerRole()
     * ```
     *
     * @param issuerId issuerId
     * @param issuerEoa issuerEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function addIssuerRole(
        bytes32 issuerId,
        address issuerEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, issuerEoa, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // issuerID有効性チェック
        {
            (bool success, string memory errTmp) = _hasIssuer(issuerId);
            require(success, errTmp);
        }

        require(issuerEoa != address(0), Error.RV0006_ISSUER_INVALID_VAL);

        _contractManager.accessCtrl().addRoleByIssuer(
            issuerId,
            _issuerData.getIssuerRole(issuerId),
            issuerEoa
        );

        emit AddIssuerRole(issuerId, issuerEoa, traceId);
    }

    /**
     * @dev account権限の追加。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param accountEoa accountEoa
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addAccountRole(
        bytes32 issuerId,
        bytes32 accountId,
        address accountEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        require(issuerId != 0x00, Error.RV0006_ISSUER_INVALID_VAL);
        // accountId存在性チェック
        {
            (bool success, string memory errTmp) = _hasAccount(issuerId, accountId);
            require(success, errTmp);
        }
        // Issuer権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, accountId, accountEoa, deadline));
            _issuerOnly(issuerId, hash, deadline, signature);
        }
        //Accountコントラクトにて登録
        _contractManager.account().addAccountRole(accountId, accountEoa, traceId);
    }

    /**
     * @dev アカウントの状態を更新する
     * _
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param accountStatus アカウントステータス
     * @param reasonCode 理由コード
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setAccountStatus(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 accountStatus,
        bytes32 reasonCode,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        require(issuerId != 0x00, Error.RV0006_ISSUER_INVALID_VAL);
        // accountId存在性チェック
        {
            (bool success, string memory errTmp) = _hasAccount(issuerId, accountId);
            require(success, errTmp);
        }
        // Issuer権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, accountId, reasonCode, deadline));
            _issuerOnly(issuerId, hash, deadline, signature);
        }

        _contractManager.account().setAccountStatus(accountId, accountStatus, reasonCode, traceId);
    }

    /**
     * @dev issuer名の更新。
     *
     * ```
     * emit event: ModIssuer()
     * ```
     *
     * @param issuerId issuerId
     * @param name issuer名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modIssuer(
        bytes32 issuerId,
        string memory name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, name, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // issuerID登録済みチェック
        {
            (bool success, string memory errTmp) = _hasIssuer(issuerId);
            require(success, errTmp);
        }

        _issuerData.modIssuer(issuerId, name);

        emit ModIssuer(issuerId, name, traceId);
    }

    /**
     * @dev Accountの限度額を更新する。
     *
     * ```
     * emit event: ModTokenLimit()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param limitUpdates アカウント限度額の更新フラグ
     * @param limitValues アカウントの限度額値
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function modTokenLimit(
        bytes32 issuerId,
        bytes32 accountId,
        AccountLimitUpdates memory limitUpdates,
        AccountLimitValues memory limitValues,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        {
            (bool success, string memory errExist) = _hasAccount(issuerId, accountId);
            require(success, errExist);
        }
        // AccountIdの解約確認を行う
        {
            (bool terminated, string memory err) = _contractManager.account().isTerminated(
                accountId
            );
            require(bytes(err).length == 0, err);
            require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);
        }
        // 権限チェック
        bytes32 hash = keccak256(
            abi.encode(issuerId, accountId, limitUpdates, limitValues, deadline)
        );
        {
            (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
                _issuerData.getIssuerRole(issuerId),
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.GA0007_ISSUER_NOT_ROLE);
        }
        // Event用に設定後のAccountの限度額Listを作成
        limitValues = _contractManager.financialZoneAccount().modAccountLimit(
            accountId,
            limitUpdates,
            limitValues
        );

        // Event用にAccountが紐づくValidatorのIdを取得する
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        // emit event
        emit ModTokenLimit(validatorId, accountId, limitUpdates, limitValues, traceId);
    }

    /**
     * @dev Accountの累積限度額初期化。
     *
     * ```
     * emit event: CumulativeReset()
     * ```
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function cumulativeReset(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // issuerId未入力チェック
        require(issuerId != 0x00, Error.RV0006_ISSUER_INVALID_VAL);
        // accountId未入力チェック
        require(accountId != 0x00, Error.RV0007_ACCOUNT_INVALID_VAL);
        // issuerIdと紐付いてるかチェック
        {
            (bool success, string memory errExist) = _hasAccount(issuerId, accountId);
            require(success, errExist);
        }
        // AccountIdの解約確認を行う
        {
            (bool terminated, string memory err) = _contractManager.account().isTerminated(
                accountId
            );
            require(bytes(err).length == 0, err);
            require(!terminated, Error.GE2007_ACCOUNT_TERMINATED);
        }
        // 権限チェック
        bytes32 hash = keccak256(abi.encode(issuerId, accountId, deadline));
        {
            (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
                _issuerData.getIssuerRole(issuerId),
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.GA0007_ISSUER_NOT_ROLE);
        }

        // FinancialZoneAccount側で累積限度額リセット
        _contractManager.financialZoneAccount().cumulativeReset(accountId);

        // Event用にAccountが紐づくValidatorのIdを取得する
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        // emit event
        emit CumulativeReset(issuerId, accountId, traceId, validatorId);
    }

    /**
     * @dev アカウントを強制償却させる
     * AccountコントラクトのforceBurnを呼び出す。
     *
     * @param issuerId 発行者ID
     * @param accountId アカウントID
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署
     */
    function forceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // issuerIdと紐付いてるかチェック
        {
            (bool success, string memory err) = _hasAccount(issuerId, accountId);
            require(success, err);
        }

        // 権限チェック
        bytes32 hash = keccak256(abi.encode(issuerId, accountId, deadline));
        {
            (bool has, string memory err) = _contractManager.accessCtrl().checkRole(
                _issuerData.getIssuerRole(issuerId),
                hash,
                deadline,
                signature
            );
            require(bytes(err).length == 0, err);
            require(has, Error.GA0007_ISSUER_NOT_ROLE);
        }

        _contractManager.account().forceBurn(accountId, traceId);
    }

    /**
     * @dev Fin Account 及び Biz Accountの部分強制償却を行う。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param burnedAmount 償却する金額
     * @param burnedBalance 償却後に残す金額
     * @param traceId トレースID
     * @param deadline signatureのタイム스타ンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function partialForceBurn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 burnedAmount,
        uint256 burnedBalance,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        IssuerLib.partialForceBurn(
            _issuerData,
            _contractManager,
            issuerId,
            accountId,
            burnedAmount,
            burnedBalance,
            traceId,
            deadline,
            signature
        );
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署
     */
    function deleteBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, zoneId, deadline));
            _adminOnly(hash, deadline, signature);
        }

        _contractManager.provider().deleteBizZoneToIssuer(issuerId, zoneId);

        // emit event
        emit DeleteBizZoneToIssuer(issuerId, zoneId, traceId);
    }

    /**
     * @dev Biz ZoneとIssuerの紐付け。
     *
     * @param issuerId issuerId
     * @param zoneId zoneId
     * @param traceId traceId
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署
     */
    function addBizZoneToIssuer(
        bytes32 issuerId,
        uint16 zoneId,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(issuerId, zoneId, deadline));
            _adminOnly(hash, deadline, signature);
        }

        _contractManager.provider().addBizZoneToIssuer(issuerId, zoneId);

        // emit event
        emit AddBizZoneToIssuer(issuerId, zoneId, traceId);
    }

    /**
     * @dev 指定されたissuerIdに紐づくIssuer情報を登録、もしくは上書きする
            バックアップリストア作業時のみ実行
     *
     * @param issuer Issuer情報
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setIssuerAll(
        IssuerAll memory issuer,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        bytes32 hash = keccak256(abi.encode(_SET_ISSUERS_ALL_SIGNATURE, deadline));
        _adminOnly(hash, deadline, signature);

        RemigrationLib.setIssuerAll(
            _issuerData[issuer.issuerId],
            _issuerIds,
            _issuerIdExistence,
            _accountIdExistenceByIssuerId,
            address(_contractManager),
            issuer
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev issuerの存在確認。
     *
     * @param issuerId issuerId
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasIssuer(bytes32 issuerId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasIssuer(issuerId);
    }

    /**
     * @dev issuerの存在確認(内部関数)。
     *
     * @param issuerId issuerId
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function _hasIssuer(bytes32 issuerId) internal view returns (bool success, string memory err) {
        return IssuerLib.hasIssuer(issuerId, _issuerIdExistence[issuerId]);
    }

    /**
     * @dev issuerのリストを取得する。
     * TODO: DeccuretIssuerを含む前Issuerを返却する関数を別途作成する
     *
     * @param limit limit
     * @param offset offset
     * @return issuers issuers
     * @return totalCount issuerの数
     * @return err zoneId
     */
    function getIssuerList(uint256 limit, uint256 offset)
        external
        view
        override
        returns (
            IssuerListData[] memory issuers,
            uint256 totalCount,
            string memory err
        )
    {
        uint256 issuerIdsLength = _issuerIds.length > 0 ? _issuerIds.length - 1 : 0;
        // issuer[0]はDeccuretIssuerのため対象外とする
        if (limit == 0 || issuerIdsLength == 0) {
            return (issuers, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (issuers, _EMPTY_LENGTH, Error.UE0102_ISSUER_TOO_LARGE_LIMIT);
        }
        if (offset >= issuerIdsLength) {
            return (issuers, _EMPTY_LENGTH, Error.UE0103_ISSUER_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (issuerIdsLength >= offset + limit) ? limit : issuerIdsLength - offset;
        // issuer[0] を無効なissuerとみなしてスキップするための補正
        uint256 effectiveOffset = offset + 1;

        issuers = new IssuerListData[](size);
        for (uint256 i = 0; i < size; i++) {
            (bytes32 issuerId, ) = this.getIssuerId(effectiveOffset + i);
            issuers[i].issuerId = issuerId;
            issuers[i].name = _issuerData[issuerId].name;
            issuers[i].bankCode = _issuerData[issuerId].bankCode;
        }
        return (issuers, issuerIdsLength, "");
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認を行う。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        {
            return _hasAccount(issuerId, accountId);
        }
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認を行う(内部関数)。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function _hasAccount(bytes32 issuerId, bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        // IssuerID存在確認
        {
            (success, err) = _hasIssuer(issuerId);
            require(success, err);
        }
        return IssuerLib.hasAccount(accountId, _accountIdExistenceByIssuerId[issuerId][accountId]);
    }

    /**
     * @dev issuerの情報を取得する。
     *
     * @param issuerId issuerId
     * @param name issuerの名前
     * @param err エラーメッセージ
     */
    function getIssuer(bytes32 issuerId)
        external
        view
        override
        returns (
            string memory name,
            uint16 bankCode,
            string memory err
        )
    {
        bool success;
        (success, err) = _hasIssuer(issuerId);
        if (!success) {
            return ("", 0, err);
        }

        return (_issuerData[issuerId].name, _issuerData[issuerId].bankCode, "");
    }

    /**
     * @dev Issuerに紐づくAccountの情報を取得する。
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @return accountName アカウント名
     * @return balance アカウントの残高
     * @return accountStatus アカウントの状態
     * @return reasonCode reasonCode
     * @return err エラーメッセージ
     */
    function getAccount(bytes32 issuerId, bytes32 accountId)
        external
        view
        override
        returns (
            string memory accountName,
            uint256 balance,
            bytes32 accountStatus,
            bytes32 reasonCode,
            string memory err
        )
    {
        bool success;
        (success, err) = _hasAccount(issuerId, accountId);
        if (!success) {
            return ("", _EMPTY_UINT, _EMPTY_BYTES32, _EMPTY_BYTES32, err);
        }

        (AccountDataWithoutZoneId memory accountData, string memory errTmp) = _contractManager
            .account()
            .getAccount(accountId);

        if (bytes(errTmp).length != 0) {
            return ("", _EMPTY_UINT, _EMPTY_BYTES32, _EMPTY_BYTES32, errTmp);
        }

        {
            accountName = accountData.accountName;
            balance = accountData.balance;
            accountStatus = accountData.accountStatus;
            reasonCode = accountData.reasonCode;
        }

        return (accountName, balance, accountStatus, reasonCode, "");
    }

    /**
     * @dev 該当IssuerIDに紐づくAccountIDを取得する。
     *
     * @param issuerId issuerId
     * @param inAccountIds inAccountIds
     * @return accounts 指定のアカウントを取得する
     * @return totalCount アカウントの総数
     * @return err エラーメッセージ
     */
    function getAccountList(
        bytes32 issuerId,
        bytes32[] memory inAccountIds,
        uint256 limit,
        uint256 offset
    )
        external
        view
        returns (
            IssuerAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        if (limit == 0 || inAccountIds.length == 0) {
            return (accounts, _EMPTY_LENGTH, "");
        }
        if (limit > _MAX_LIMIT) {
            return (accounts, _EMPTY_LENGTH, Error.UE0111_VALIDATOR_TOO_LARGE_LIMIT);
        }
        if (offset >= inAccountIds.length) {
            return (accounts, _EMPTY_LENGTH, Error.UE0112_VALIDATOR_OFFSET_OUT_OF_INDEX);
        }

        // 配列のサイズを作成する。
        // 要素数がoffsetとlimitの指定範囲以上の配列である場合はlimit分の配列を作成する
        // 要素数がoffsetとlimitの指定範囲未満の配列である場合は範囲分の項目数のみの配列を作成する
        uint256 size = (inAccountIds.length >= offset + limit)
            ? limit
            : inAccountIds.length - offset;

        accounts = new IssuerAccountsData[](size);

        for (uint256 i = 0; i < size; i++) {
            bytes32 accountId = inAccountIds[offset + i];
            bool success;
            AccountDataWithoutZoneId memory accountData;

            // issuerIdに紐づくaccountIdが存在するか確認
            (success, ) = _hasAccount(issuerId, accountId);
            if (success) {
                accounts[i].accountId = accountId;
                (accountData, ) = _contractManager.account().getAccount(accountId);
                accounts[i].balance = accountData.balance;
                accounts[i].accountStatus = accountData.accountStatus;
                accounts[i].reasonCode = accountData.reasonCode;
            } else {
                accounts[i].accountId = 0;
                accounts[i].balance = 0;
                accounts[i].accountStatus = "";
                accounts[i].reasonCode = 0;
            }
        }

        return (accounts, _issuerData[issuerId].accountIds.length, "");
    }

    /**
     * @dev issuerの数を返却する。
     *
     * @return count issuerの数
     */
    function getIssuerCount() external view override returns (uint256 count) {
        // List内にDeccuretのIssuerが含まれる
        return _issuerIds.length;
    }

    /**
     * @dev indexに対応するissuerIdの取得。
     *
     * @param index index
     * @return issuerId issuerId
     * @return err エラーメッセージ
     */
    function getIssuerId(uint256 index)
        external
        view
        override
        returns (bytes32 issuerId, string memory err)
    {
        return _issuerIds.getIssuerId(index);
    }

    /**
     * @dev 権限チェック。
     *
     * @param issuerId issuerId
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function checkRole(
        bytes32 issuerId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view override returns (bool has, string memory err) {
        if (issuerId == 0x00) {
            return (false, Error.RV0006_ISSUER_INVALID_VAL);
        }
        // 権限チェック
        (has, err) = _contractManager.accessCtrl().checkRole(
            _issuerData.getIssuerRole(issuerId),
            hash,
            deadline,
            signature
        );
    }

    /**
     * @dev FinZoneコイン発行前確認
     *
     * @param issuerId issuerId
     * @param accountId accountId
     * @param amount Mintする数量
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     *
     * @return success true:チェックOK,false:チェックNG
     * @return err エラーメッセージ
     */
    function checkMint(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        //Issuer権限確認
        {
            bytes32 hash = keccak256(abi.encode(issuerId, accountId, amount, deadline));
            (success, err) = _contractManager.issuer().checkRole(
                issuerId,
                hash,
                deadline,
                signature
            );
            if (bytes(err).length != 0) {
                return (false, err);
            }
            if (!success) {
                return (success, Error.GA0007_ISSUER_NOT_ROLE);
            }
        }
        //Token存在確認
        {
            (success, err) = _contractManager.token().hasTokenState();
            if (!success) {
                return (success, err);
            }
        }

        // ゾーンIDが取得できるかチェック
        (, , err) = _contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (success, err);
        }
        // 限度額チェック
        (success, err) = _contractManager.financialZoneAccount().checkMint(accountId, amount);
        if (!success) {
            return (success, err);
        }
        return (success, "");
    }

    /**
     * @dev アカウントの凍結状態を確認する。
     *
     * @param accountId アカウントID
     * @return frozen true:凍結中,false:凍結中でない
     * @return err エラーメッセージ
     */
    function isFrozen(bytes32 accountId) external view returns (bool frozen, string memory err) {
        return _contractManager.account().isFrozen(accountId);
    }

    /**
     * @dev FinZoneコイン償却前確認
     *
     * @param issuerId issuerId
     * @param accountId アカウントID
     * @param amount 償却額
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 署名
     */
    function checkBurn(
        bytes32 issuerId,
        bytes32 accountId,
        uint256 amount,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        //Issuer権限確認
        {
            bytes32 hash = keccak256(abi.encode(issuerId, accountId, amount, deadline));
            (success, err) = _contractManager.issuer().checkRole(
                issuerId,
                hash,
                deadline,
                signature
            );
            if (bytes(err).length != 0) {
                return (false, err);
            }
            if (!success) {
                return (success, Error.GA0007_ISSUER_NOT_ROLE);
            }
        }
        //Token存在確認
        {
            (success, err) = _contractManager.token().hasTokenState();
            if (!success) {
                return (success, err);
            }
        }
        // ゾーンIDが取得できるかチェック
        (, , err) = _contractManager.provider().getZone();
        if (bytes(err).length != 0) {
            return (success, err);
        }
        // 限度額チェック
        (success, err) = _contractManager.financialZoneAccount().checkBurn(accountId, amount);
        if (!success) {
            return (success, err);
        }
        // 残高チェック
        uint256 balance;
        (balance, err) = _contractManager.account().balanceOf(accountId);
        if (balance < amount) {
            return (false, err);
        }
        return (success, "");
    }

    /**
     *
     * @param index index
     */
    function getIssuerAll(uint256 index) external view returns (IssuerAll memory issuer) {
        bytes32 issuerId = _issuerIds[index];
        return
            RemigrationLib.getIssuerAll(
                _issuerData[issuerId],
                _issuerIds,
                _issuerIdExistence,
                _accountIdExistenceByIssuerId,
                address(_contractManager),
                index
            );
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
