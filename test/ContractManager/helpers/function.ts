import { BASE } from '@test/common/consts'
import { castReturnType, getDeadline } from '@test/common/utils'
import { FunctionType } from './types'
import privateKey from '@/privateKey'

// ライブラリとコントラクトのリンクを初回のみ実施するため、グローバル変数としてフラグを利用する
const flag = false

/**
 * contractManagerのイベントを呼ぶ関数を持つobject
 */
export const contractManagerFuncs: FunctionType = {
  version: ({ contractManager }) => {
    return castReturnType(contractManager.version())
  },
  setIbcApp: async ({ options = {}, ...args }) => {
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const signer = privateKey.key[eoaKey]
    const _sig = sig ?? privateKey.sig(signer, ['address', 'uint256'], [args.ibcAddress, _deadline])

    return castReturnType(
      args.contractManager.connect(args.accounts[0]).setIbcApp(args.ibcAddress, args.ibcAppName, _deadline, _sig[0]),
    )
  },
  setContracts: async ({ contractManager, accounts, addresses, options = {} }) => {
    const jsonAddresses = {
      ctrlAddress: addresses[0],
      providerAddress: addresses[1],
      issuerAddress: addresses[2],
      validatorAddress: addresses[3],
      validatorStorageAddress: addresses[4],
      accountAddress: addresses[5],
      financialZoneAccountAddress: addresses[6],
      businessZoneAccountAddress: addresses[7],
      tokenAddress: addresses[8],
      ibcTokenAddress: addresses[9],
      financialCheckAddress: addresses[10],
      transferProxyAddress: addresses[11],
    }
    const { deadline, sig, eoaKey = BASE.EOA.ADMIN } = options
    const _deadline = await getDeadline(deadline)
    const _sig =
      sig ??
      privateKey.sig(
        privateKey.key[eoaKey],
        [
          {
            type: 'tuple',
            components: [
              { type: 'address', name: 'ctrlAddress' },
              { type: 'address', name: 'providerAddress' },
              { type: 'address', name: 'issuerAddress' },
              { type: 'address', name: 'validatorAddress' },
              { type: 'address', name: 'validatorStorageAddress' },
              { type: 'address', name: 'accountAddress' },
              { type: 'address', name: 'financialZoneAccountAddress' },
              { type: 'address', name: 'businessZoneAccountAddress' },
              { type: 'address', name: 'tokenAddress' },
              { type: 'address', name: 'ibcTokenAddress' },
              { type: 'address', name: 'financialCheckAddress' },
              { type: 'address', name: 'transferProxyAddress' },
            ],
          },
          'uint256',
        ],
        [jsonAddresses, _deadline],
      )

    return contractManager.connect(accounts[9]).setContracts(jsonAddresses, _deadline, _sig[0])
  },
  accessCtrl: ({ contractManager }) => {
    return castReturnType(contractManager.accessCtrl())
  },
  provider: ({ contractManager }) => {
    return castReturnType(contractManager.getFunction('provider'))
  },
  account: ({ contractManager }) => {
    return castReturnType(contractManager.account())
  },
  financialZoneAccount: ({ contractManager }) => {
    return castReturnType(contractManager.financialZoneAccount())
  },
  businessZoneAccount: ({ contractManager }) => {
    return castReturnType(contractManager.businessZoneAccount())
  },
  validator: ({ contractManager }) => {
    return castReturnType(contractManager.validator())
  },
  validatorStorage: ({ contractManager }) => {
    return castReturnType(contractManager.validatorStorage())
  },
  issuer: ({ contractManager }) => {
    return castReturnType(contractManager.issuer())
  },
  token: ({ contractManager }) => {
    return castReturnType(contractManager.token())
  },
  ibcToken: ({ contractManager }) => {
    return castReturnType(contractManager.ibcToken())
  },
  financialCheck: ({ contractManager }) => {
    return castReturnType(contractManager.financialCheck())
  },
  transferProxy: ({ contractManager }) => {
    return castReturnType(contractManager.transferProxy())
  },
  ibcApp: ({ contractManager, ibcAppName }) => {
    return castReturnType(contractManager.ibcApp(ibcAppName))
  },
}
