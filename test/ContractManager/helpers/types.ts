import { SignerWithAddress } from '@nomicfoundation/hardhat-ethers/signers'
import {
  AccessCtrlInstance,
  AccountInstance,
  BusinessZoneAccountInstance,
  ContractCallOption,
  ContractManagerInstance,
  FinancialCheckInstance,
  FinancialZoneAccountInstance,
  IBCTokenInstance,
  IssuerInstance,
  ProviderInstance,
  TokenInstance,
  TransferProxyInstance,
  ValidatorInstance,
  ValidatorStorageInstance,
} from '@test/common/types'
import { ContractTransactionResponse } from 'ethers'
import { TypedContractMethod } from '@/types/common'

export type ContractManagerContractType = {
  accounts: SignerWithAddress[]
  contractManager: ContractManagerInstance
  accessCtrl: AccessCtrlInstance
  provider: ProviderInstance
  issuer: IssuerInstance
  validator: ValidatorInstance
  validatorStorage: ValidatorStorageInstance
  account: AccountInstance
  financialZoneAccount: FinancialZoneAccountInstance
  businessZoneAccount: BusinessZoneAccountInstance
  token: TokenInstance
  financialCheck: FinancialCheckInstance
  transferProxy: TransferProxyInstance
  ibcToken: IBCTokenInstance
}

type ContractManagerType = { contractManager: ContractManagerInstance }

type BaseAccountsType = ContractManagerType & {
  accounts: SignerWithAddress[]
  options?: Partial<ContractCallOption>
}

export type FuncParamsType = {
  version: ContractManagerType
  setIbcApp: BaseAccountsType & {
    ibcAddress: string
    ibcAppName: string
  }
  setContracts: BaseAccountsType & {
    addresses: string[]
  }
  accessCtrl: ContractManagerType
  provider: ContractManagerType
  account: ContractManagerType
  financialZoneAccount: ContractManagerType
  businessZoneAccount: ContractManagerType
  validator: ContractManagerType
  validatorStorage: ContractManagerType
  issuer: ContractManagerType
  token: ContractManagerType
  ibcToken: ContractManagerType
  financialCheck: ContractManagerType
  transferProxy: ContractManagerType
  ibcApp: ContractManagerType & { ibcAppName: string }
}

type FuncReturnType = {
  version: string
  setIbcApp: ContractTransactionResponse
  setContracts: ContractTransactionResponse
  accessCtrl: string
  provider: TypedContractMethod<[], [string], 'view'>
  account: string
  financialZoneAccount: string
  businessZoneAccount: string
  validator: string
  issuer: string
  token: string
  ibcToken: string
  financialCheck: string
  transferProxy: string
  ibcApp: string
}

export type FunctionType = {
  [K in keyof FuncParamsType]: (args: FuncParamsType[K]) => Promise<FuncReturnType[K]>
}
